{"name": "sensefolks_webapp", "private": true, "version": "0.0.1", "description": "Make informed decisions with focused feedback", "scripts": {"build": "stencil build", "start": "stencil build --dev --watch --serve", "test": "stencil test --spec --e2e", "test.watch": "stencil test --spec --e2e --watchAll", "generate": "stencil generate"}, "devDependencies": {"@stencil/core": "4.22.2", "@stencil/store": "^2.0.16", "@types/jest": "^29.5.6", "jest": "^29.7.0", "jest-cli": "^29.7.0", "puppeteer": "^21.11.0", "stencil-router-v2": "^0.6.0"}, "license": "MIT", "dependencies": {"joi": "^17.13.3"}}